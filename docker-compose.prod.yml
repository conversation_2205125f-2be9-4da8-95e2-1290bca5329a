# Configuração otimizada para produção
version: '3.8'

services:
  trade-api:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: trade-api-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env
    volumes:
      - ./db:/app/db
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - trade-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  trade-bot:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: trade-bot-prod
    restart: unless-stopped
    command: ["node", "dist/index.js"]
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./db:/app/db
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - trade-network
    depends_on:
      trade-api:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    container_name: trade-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./src/frontend/dist:/usr/share/nginx/html:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - trade-network
    depends_on:
      trade-api:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    container_name: trade-redis-prod
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - trade-network
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # Watchtower para atualizações automáticas (opcional)
  watchtower:
    image: containrrr/watchtower
    container_name: trade-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600
      - WATCHTOWER_INCLUDE_STOPPED=true
    networks:
      - trade-network

networks:
  trade-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  nginx-logs:
    driver: local