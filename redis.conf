# Configuração Redis para produção

# Configurações básicas
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# Configurações de memória
maxmemory 200mb
maxmemory-policy allkeys-lru

# Configurações de persistência
save 900 1
save 300 10
save 60 10000

# Configurações de log
loglevel notice
logfile ""

# Configurações de segurança
# requirepass your_redis_password_here

# Configurações de performance
tcp-backlog 511
databases 16

# Configurações de rede
# maxclients 10000

# Configurações de append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Configurações de slow log
slowlog-log-slower-than 10000
slowlog-max-len 128